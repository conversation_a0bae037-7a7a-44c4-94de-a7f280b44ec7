import {
  useInfiniteQuery as useRNInfiniteQuery,
  type UseInfiniteQueryOptions as UseRNInfiniteQueryOptions,
} from '@tanstack/react-query'
import { flatten, uniqBy } from 'lodash'
import { useCallback, useMemo } from 'react'

import {
  ensureQueryKey,
  getCacheOptions,
  runWithTimeout,
} from 'Services/QueryClient/utils'

import useTriggerWhenDone from './useTriggerWhenDone'
import { _queryClient } from './QueryClient'

export type InfiniteQueryFn<T = any[]> = (_: { page: number }) => Promise<T[]>

const flatArr = (arr: any) =>
  arr?.length ? flatten(arr.map((x: any) => x.data)).filter(Boolean) : []

export interface UseInfiniteQueryOptions
  extends Partial<UseRNInfiniteQueryOptions> {
  timeout?: number
  maxPage?: number
  cacheForHours?: number
  noCache?: boolean
  suspense?: boolean
  uniqBy?: string | ((item: any) => string)
}

const useInfiniteQuery = <T, _ = unknown>(
  keys: any[],
  fn: InfiniteQueryFn<T>,
  opts?: UseInfiniteQueryOptions,
) => {
  const {
    fetchNextPage,
    isFetchingNextPage,
    error,
    refetch: _refetch,
    isRefetching,
    isFetching,
    data,
    hasNextPage,
    ...rest
  } = useRNInfiniteQuery(
    {
      networkMode: 'offlineFirst',
      ...(opts as any),
      queryKey: ensureQueryKey(keys),
      initialPageParam: 0,
      queryFn: async ({ pageParam = 0 }) => {
        // const d = await fn({ page })
        const page = Number(pageParam)

        const { promise, cancel } = runWithTimeout(
          () => fn?.({ page }),
          opts?.timeout || 0,
          3,
        )
        const d = await promise
        cancel()

        return {
          data: d,
          nextCursor:
            d?.length &&
            ((opts?.maxPage && page + 1 < opts?.maxPage) || !opts?.maxPage)
              ? page + 1
              : undefined,
        }
      },
      getNextPageParam: lastPage => lastPage?.nextCursor,
      ...(opts?.cacheForHours &&
        getCacheOptions(opts?.cacheForHours, opts?.staleTime as number)),
      ...(opts?.noCache && getCacheOptions(0)),
    },
    _queryClient,
  )

  const loadMore = useTriggerWhenDone({
    disabled: !isFetching && !hasNextPage,
    whenDone: !isFetching,
    trigger: fetchNextPage,
  })

  const flatData = useMemo(() => {
    const fattened = flatArr(data?.pages)
    if (opts?.uniqBy) return uniqBy(fattened, opts.uniqBy)
    return fattened
  }, [data?.pages, opts?.uniqBy])

  const refetch = useCallback(
    (props?: any) => {
      if (isFetching || isRefetching) return
      _refetch?.(props)
    },
    [_refetch, isFetching, isRefetching],
  )

  return {
    ...rest,
    hasNextPage,
    data: flatData as T[],
    isRefetching,
    isFirstLoading: isFetching,
    isLoading: isRefetching || rest?.isLoading || rest?.isPending,
    isLoadingMore: isFetchingNextPage,
    loadMore,
    error,
    refetch,
  }
}

export default useInfiniteQuery
