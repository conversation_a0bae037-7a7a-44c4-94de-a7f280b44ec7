import { useEffect } from 'react'

import { ToastService } from 'Components/Toast'
import QueryClient from 'Services/QueryClient'
import useInfiniteQuery from 'Services/QueryClient/useInfiniteQuery'
import { getCurrentUserId } from 'Features/Auth/Stores'
import Analytics from 'Trackings/Analytics'

import type { QueryOrchestratorOptions } from './QueryOrchestrator.d'
import { type NewLibraryData, NewLibraryTab } from '../NewLibrary.d'
import { getCurrentTabVersion } from '../Stores/NewLibraryStore'
import queryArchived from '../Tabs/Archived/queryArchived'
import queryBookmarks from '../Tabs/Bookmarks/queryBookmarks'
import queryDownloaded from '../Tabs/Downloaded/queryDownloaded'
import queryFavorite from '../Tabs/Favorite/queryFavorite'
import queryPurchased from '../Tabs/Purchased/queryPurchased'
import AutoRefreshRecentListener from '../Tabs/Recent/AutoRefreshRecentListener'
import queryRecent from '../Tabs/Recent/queryRecent'

const FUNC = {
  [NewLibraryTab.Recent]: queryRecent,
  [NewLibraryTab.Purchased]: queryPurchased,
  [NewLibraryTab.Downloaded]: queryDownloaded,
  [NewLibraryTab.Favorite]: queryFavorite,
  [NewLibraryTab.Bookmarks]: queryBookmarks,
  [NewLibraryTab.Archived]: queryArchived,
}

const LONG_LOADING_THRESHOLD = 5000 // 5 seconds
const KEY = 'new_library'

const QueryOrchestrator = {
  clearCache: () => {
    QueryClient._invalidateQueries({ queryKey: [KEY, getCurrentUserId()] })
  },
  getQueryKey: (tab: NewLibraryTab, options: QueryOrchestratorOptions) =>
    [
      KEY,
      getCurrentUserId(),
      tab,
      options?.filter,
      options?.sort,
      options?.version || getCurrentTabVersion(tab),
    ].filter(Boolean),
  useLibraryData: (tab: NewLibraryTab, options: QueryOrchestratorOptions) => {
    const queryFn = FUNC[tab] || (() => [])

    return useInfiniteQuery<NewLibraryData>(
      QueryOrchestrator.getQueryKey(tab, options),
      async ({ page }) => {
        const startTime = Date.now()
        const toastRef = setTimeout(() => {
          ToastService.show({
            text: 'Thời gian tải lâu hơn dự kiến, bạn thông cảm đợi một chút nhé!',
            duration: LONG_LOADING_THRESHOLD,
            type: 'info',
          })
        }, LONG_LOADING_THRESHOLD)

        const response = await queryFn({ page, ...options })

        const duration = Date.now() - startTime
        if (duration > LONG_LOADING_THRESHOLD) {
          // If the duration is more than 5 seconds, track it
          Analytics.timeLoadLibrary(duration)
        } else {
          // If the duration is less than 5 seconds, clear the toast
          clearTimeout(toastRef)
        }
        return response
      },
      {
        cacheForHours: 24 * 7, // Cache for 1 week
        staleTime: 0,
        refetchOnMount: false,
      },
    )
  },

  useAutoRefreshListener: (tab: NewLibraryTab) => {
    useEffect(() => {
      switch (tab) {
        case NewLibraryTab.Recent:
          return AutoRefreshRecentListener()
      }
    }, [tab])
  },
}

export default QueryOrchestrator
